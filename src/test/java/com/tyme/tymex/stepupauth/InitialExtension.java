package com.tyme.tymex.stepupauth;

import java.io.BufferedInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import lombok.SneakyThrows;
import lombok.extern.log4j.Log4j2;
import okhttp3.mockwebserver.Dispatcher;
import okhttp3.mockwebserver.MockResponse;
import okhttp3.mockwebserver.MockWebServer;
import okhttp3.mockwebserver.RecordedRequest;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.extension.BeforeAllCallback;
import org.junit.jupiter.api.extension.ExtensionContext;
import org.springframework.http.HttpMethod;
import org.springframework.test.context.DynamicPropertyRegistry;
import org.springframework.test.context.DynamicPropertySource;
import org.testcontainers.containers.Network;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.containers.localstack.LocalStackContainer.Service;
import org.testcontainers.utility.DockerImageName;

import static org.awaitility.Awaitility.await;

@Log4j2
public class InitialExtension implements BeforeAllCallback,
    ExtensionContext.Store.CloseableResource {

  private static boolean started = false;
  public static MockWebServer deviceServiceMock;
  public static MockWebServer profileServiceMock;
  public static LocalStackContainer localstackContainer = null;

  private static boolean isPipeline() {
    return "true".equals(System.getProperty("it.pipeline", "false"));
  }

  private static void startLocalStackContainer() {
    int maxRetry = 3;
    int attempt = 1;
    boolean retry = true;
    do {
      if (!retry) {
        break;
      }
      try {
        await().atMost(2, TimeUnit.SECONDS).until(() -> {
          log.info("Sleep 2s");
          return true;
        });
        localstackContainer = new LocalStackContainer(DockerImageName.parse(isPipeline()
            ? ("665484546001.dkr.ecr.eu-west-1.amazonaws.com/localstack-docker-image:2.1.0")
            : "localstack/localstack:3.1.0"))
            .withServices(LocalStackContainer.Service.S3)
            .withServices(LocalStackContainer.Service.DYNAMODB)
            .withServices(LocalStackContainer.Service.SECRETSMANAGER)
            .withServices(LocalStackContainer.Service.SQS)
            .withServices(Service.CLOUDFORMATION)
            .withNetwork(Network.SHARED);
        localstackContainer.start();
        log.info("LocalStackContainer is running");
        retry = false;
      } catch (Exception ex) {
        log.error(ex.getMessage(), ex);
      }
      log.info("Retry LocalStackContainer {}", attempt);
    } while (attempt++ <= maxRetry);
  }

  public static void deviceServiceMockStart() {
    deviceServiceMock = new MockWebServer();
    try {
      deviceServiceMock.setDispatcher(new Dispatcher() {
        @NotNull
        @Override
        public MockResponse dispatch(@NotNull RecordedRequest request) {
          try {
            String stringBody = request.getBody().readUtf8();
            if (HttpMethod.POST.name().equals(request.getMethod()) &&
                Objects.requireNonNull(request.getPath()).equals("/intapi/devices/info")) {
              if (stringBody.contains("passed-device-linking")) {
                return new MockResponse()
                    .addHeader("Content-Type", "application/json")
                    .setResponseCode(200)
                    .setBody(
                        "{ \"profileId\": \"12345\", \"deviceId\": \"abcde12345\", \"linkedAt\": 1726803180000, \"status\": \"ACTIVE\" }");
              }
              return new MockResponse().setResponseCode(404)
                  .setBody(
                      "{ \"errors\": [ { \"errorCode\": \"2006018\", \"errorMessage\": \"Linked device does not exist\" } ] }");
            }
          } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
          }
          return new MockResponse().addHeader("Content-Type", "application/json")
              .setResponseCode(200);
        }
      });
      deviceServiceMock.start();
      log.info("Device service mock started");
      log.info("Device service mock port: {}", deviceServiceMock.getPort());
      log.info("Device service mock url: {}", deviceServiceMock.url("/"));
      log.info("Device service mock host: {}", deviceServiceMock.getHostName());
      log.info("Device service mock address: {}", deviceServiceMock.getHostName() + ":" + deviceServiceMock.getPort());
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
  }

  public static void profileServiceMockStart() {
    profileServiceMock = new MockWebServer();
    try {
      profileServiceMock.setDispatcher(new Dispatcher() {
        @NotNull
        @Override
        public MockResponse dispatch(@NotNull RecordedRequest request) {
          try {
            // Mock profile service endpoint: GET /v1/intapi/profiles
            if (HttpMethod.GET.name().equals(request.getMethod()) &&
                    Objects.requireNonNull(request.getPath()).equals("/v1/intapi/profiles")) {

              String profileId = request.getHeader("profile-id");
              log.info("Profile service mock received request for profileId: {}", profileId);

              // Return valid profile for test profiles
              if (profileId != null && profileId.startsWith("test-profile-")) {
                // Special case: profile without phone data
                if (profileId.contains("no-phone")) {
                  return new MockResponse()
                          .addHeader("Content-Type", "application/json")
                          .setResponseCode(200)
                          .setBody("{\n" +
                                  "    \"id\": \"" + profileId + "\",\n" +
                                  "    \"clientTypeCd\": \"NATURAL_PERSON\",\n" +
                                  "    \"clientStatus\": \"ACTIVE\",\n" +
                                  "    \"reasonCodes\": [],\n" +
                                  "    \"personKycData\": {\n" +
                                  "        \"kycTypeCd\": \"F\"\n" +
                                  "    },\n" +
                                  "    \"personPhoneData\": null,\n" +
                                  "    \"personEmailData\":{\n" +
                                  "            \"email\": \"<EMAIL>\",\n" +
                                  "            \"verificationStatus\": \"VERIFIED\"\n" +
                                  "    },\n" +
                                  "    \"createdDate\": 850867200000\n" +
                                  "}");
                }

                // Normal case: profile with phone data
                return new MockResponse()
                        .addHeader("Content-Type", "application/json")
                        .setResponseCode(200)
                        .setBody("{\n" +
                                "    \"id\": \"" + profileId + "\",\n" +
                                "    \"clientTypeCd\": \"NATURAL_PERSON\",\n" +
                                "    \"clientStatus\": \"ACTIVE\",\n" +
                                "    \"reasonCodes\": [],\n" +
                                "    \"personKycData\": {\n" +
                                "        \"kycTypeCd\": \"F\"\n" +
                                "    },\n" +
                                "    \"personPhoneData\": {\n" +
                                "            \"phoneNumber\": \"+84909418781\",\n" +
                                "            \"dialCode\": \"+84\",\n" +
                                "            \"isoCode\": \"VN\",\n" +
                                "            \"verificationStatus\": \"VERIFIED\"\n" +
                                "    },\n" +
                                "    \"personEmailData\":{\n" +
                                "            \"email\": \"<EMAIL>\",\n" +
                                "            \"verificationStatus\": \"VERIFIED\"\n" +
                                "    },\n" +
                                "    \"createdDate\": 850867200000\n" +
                                "}");
              }

              // Return 404 for non-existent profiles
              return new MockResponse()
                      .setResponseCode(404)
                      .setBody(
                              "{ \"errors\": [ { \"errorCode\": \"0106002\", \"errorMessage\": \"Profile not found\", \"unifiedErrorCode\": \"2013002\" } ] }");
            }
          } catch (Exception ex) {
            log.error(ex.getMessage(), ex);
          }
          return new MockResponse()
                  .addHeader("Content-Type", "application/json")
                  .setResponseCode(200);
        }
      });
      profileServiceMock.start();
      log.info("Profile service mock started");
      log.info("Profile service mock port: {}", profileServiceMock.getPort());
      log.info("Profile service mock url: {}", profileServiceMock.url("/"));
      log.info("Profile service mock host: {}", profileServiceMock.getHostName());
      log.info("Profile service mock address: {}", profileServiceMock.getHostName() + ":" + profileServiceMock.getPort());
    } catch (Exception ex) {
      log.error(ex.getMessage(), ex);
    }
  }

  @SneakyThrows
  public static void deviceServiceMockStop() {
    if (deviceServiceMock != null) {
      deviceServiceMock.shutdown();
    }
  }
  @SneakyThrows
  public static void profileServiceMockStop() {
    if (profileServiceMock != null) {
      profileServiceMock.shutdown();
    }
  }

  private static void stopLocalStackContainer() {
    if (localstackContainer != null) {
      if (localstackContainer.isRunning()) {
        localstackContainer.stop();
      } else {
        log.error("LocalStackContainer is not running");
      }
    } else {
      log.error("LocalStackContainer is null");
    }
  }

  public static void setAWSCredentialsEnv() {
    if (localstackContainer != null) {
      System.setProperty("cloud.aws.region.static", localstackContainer.getRegion());
      System.setProperty("cloud.aws.credentials.access-key", localstackContainer.getAccessKey());
      System.setProperty("cloud.aws.credentials.secret-key", localstackContainer.getSecretKey());

      System.setProperty("cloud.aws.dynamodb.endpoint",
          localstackContainer.getEndpointOverride(LocalStackContainer.Service.DYNAMODB).toString());
      System.setProperty("cloud.aws.secrets-manager.endpoint",
          localstackContainer.getEndpointOverride(LocalStackContainer.Service.SECRETSMANAGER)
              .toString());
      System.setProperty("cloud.aws.ssm.endpoint",
          localstackContainer.getEndpointOverride(LocalStackContainer.Service.SSM).toString());

      System.setProperty("cloud.aws.sqs.endpoint",
          localstackContainer.getEndpointOverride(LocalStackContainer.Service.SQS).toString());

      System.setProperty("cloud.aws.sqs.endpoint",
          localstackContainer.getEndpointOverride(LocalStackContainer.Service.SQS).toString());

    } else {
      System.out.println("localstack container is null");
      System.exit(1);
    }
  }

  public static void setPropertyEnv() {
    System.setProperty("spring.cloud.openfeign.client.config.client-profile-ingress.url",
            String.format("http://localhost:%s", profileServiceMock.getPort()));
  }


  @Override
  public void beforeAll(ExtensionContext context) throws IOException, InterruptedException {
    initialize();
  }

  public static void initialize() throws IOException, InterruptedException {
    if (!started) {
      started = true;
      startLocalStackContainer();
      setAWSCredentialsEnv();
      createStack();
      deviceServiceMockStart();
      profileServiceMockStart();
      setPropertyEnv();
    }
  }

  @Override
  public void close() {
    tearDown();
  }

  public static void tearDown() {
    started = false;
    stopLocalStackContainer();
    deviceServiceMockStop();
    profileServiceMockStop();
  }

  @DynamicPropertySource
  static void createStacks(DynamicPropertyRegistry registry)
      throws IOException, InterruptedException {
    String sqsEndpoint = localstackContainer.getEndpointOverride(Service.SQS).toString();
    registry.add("spring.cloud.aws.sqs.endpoint", () -> sqsEndpoint);
    registry.add("spring.cloud.aws.region.static", () -> localstackContainer.getRegion());

    var reader = new BufferedInputStream(Objects.requireNonNull(
        InitialExtension.class.getResourceAsStream("/local-aws-stack.yaml")));
    String txt = new String(reader.readAllBytes(), StandardCharsets.UTF_8);
    var result = localstackContainer.execInContainer("awslocal", "cloudformation", "create-stack",
        "--stack-name",
        "STEP_UP_AUTH_INTEGRATION_TEST", "--template-body", txt, "--region",
        localstackContainer.getRegion());
    log.info("Result execute stack {}", result.toString());

  }

  private static void createStack() throws IOException, InterruptedException {
    try (BufferedInputStream reader = new BufferedInputStream(
        Objects.requireNonNull(InitialExtension.class.getResourceAsStream("/local-aws-stack.yaml")))) {
      String txt = new String(reader.readAllBytes(), StandardCharsets.UTF_8);
      var result = localstackContainer.execInContainer("awslocal", "cloudformation", "create-stack",
          "--stack-name", "STEP_UP_AUTH_INTEGRATION_TEST",
          "--template-body", txt, "--region", localstackContainer.getRegion());

      System.out.println("Result execute stack: " + result.toString());
    }
  }


}
