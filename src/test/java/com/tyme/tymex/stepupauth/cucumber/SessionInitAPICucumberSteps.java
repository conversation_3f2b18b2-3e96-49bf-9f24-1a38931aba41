package com.tyme.tymex.stepupauth.cucumber;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.tyme.tymex.stepupauth.StepUpAuthApplicationTestsBase;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto;
import com.tyme.tymex.stepupauth.controller.domain.InitStepUpSessionDto.AuthFactorRule;
import com.tyme.tymex.stepupauth.controller.domain.StepUpSessionResponse;
import com.tyme.tymex.stepupauth.domain.AuthFactor;
import com.tyme.tymex.stepupauth.domain.IdentifierType;
import com.tyme.tymex.stepupauth.domain.ProfileInfo;
import com.tyme.tymex.stepupauth.domain.StepUpStatus;
import com.tyme.tymex.stepupauth.infra.connector.model.profile.ProfilePhoneData;
import com.tyme.tymex.stepupauth.infra.dynamodb.domain.StepUpEntity;
import com.tyme.tymex.stepupauth.infra.exception.DomainException;
import com.tyme.tymex.stepupauth.infra.exception.model.ErrorCode;
import com.tyme.tymex.stepupauth.repository.StepUpRepo;
import com.tyme.tymex.stepupauth.service.StepUpService;
import com.tyme.tymex.stepupauth.utils.GlobalUtils;
import io.cucumber.java.Before;
import io.cucumber.java.en.Given;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;
import lombok.extern.log4j.Log4j2;
import org.junit.jupiter.api.Assertions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.UUID;

@Log4j2
@SpringBootTest
public class SessionInitAPICucumberSteps extends StepUpAuthApplicationTestsBase {

    @Autowired
    private StepUpService stepUpService;

    @Autowired
    private StepUpRepo stepUpRepo;

    @Autowired
    private ObjectMapper objectMapper;

    private String profileId;
    private String flowId;
    private String flowName;
    private String appId;
    private InitStepUpSessionDto initRequest;
    private StepUpSessionResponse sessionResponse;
    private Exception thrownException;
    private ProfileInfo profileInfo;

    @Before
    public void init() {
        profileId = "test-profile-" + UUID.randomUUID();
        flowId = "test-flow-" + UUID.randomUUID();
        flowName = "TEST_FLOW";
        appId = "test-app";
        initRequest = null;
        sessionResponse = null;
        thrownException = null;
        profileInfo = null;
    }

    @Given("A valid profile exists in the system")
    public void aValidProfileExistsInTheSystem() {
        // Profile will be mocked by the MockWebServer in InitialExtension
        // The mock server will return valid profile data for profileIds starting with "test-profile-"
        System.out.println(profileId);
        profileInfo = ProfileInfo.builder()
                .id(profileId)
                .personPhoneData(ProfilePhoneData.builder()
                        .dialCode("+84")
                        .phoneNumber("+84909418781")
                        .build())
                .build();
        log.info("Profile info: {}", profileInfo.toString());
        log.info("Profile service mock server will return valid profile info for profileId: {}", profileId);
    }

    @Given("The profile has valid phone number information")
    public void theProfileHasValidPhoneNumberInformation() {
        // This is already covered in the previous step
        Assertions.assertNotNull(profileInfo);
        Assertions.assertNotNull(profileInfo.personPhoneData());
        Assertions.assertNotNull(profileInfo.personPhoneData().dialCode());
        Assertions.assertNotNull(profileInfo.personPhoneData().phoneNumber());
        log.info("Profile has valid phone data: dialCode={}, phoneNumber={}", 
                profileInfo.personPhoneData().dialCode(), 
                profileInfo.personPhoneData().phoneNumber());
    }

    @When("I initialize a step-up session with OTP factor and PROFILE_ID")
    public void iInitializeAStepUpSessionWithOTPFactorAndPROFILEID() {
        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(AuthFactor.OTP)
                        .build()))
                .identifierId(profileId)
                .identifierType(IdentifierType.PROFILE_ID)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with request: {}", initRequest.getIdentifierId());

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during session initialization: {}", e.getMessage());
        }
    }

    @Then("The session should be created successfully")
    public void theSessionShouldBeCreatedSuccessfully() {
        Assertions.assertNull(thrownException, "No exception should be thrown");
        Assertions.assertNotNull(sessionResponse, "Session response should not be null");
        Assertions.assertNotNull(sessionResponse.getStepUpAuthId(), "Step-up auth ID should not be null");
        log.info("Session created successfully with ID: {}", sessionResponse.getStepUpAuthId());
    }

    @Then("The session should have OTP factor configured")
    public void theSessionShouldHaveOTPFactorConfigured() throws JsonProcessingException {
        Assertions.assertNotNull(sessionResponse.getAuthFactorRules(), "Auth factor rules should not be null");
        Assertions.assertFalse(sessionResponse.getAuthFactorRules().isEmpty(), "Auth factor rules should not be empty");
        
        // Parse JSON string to List<AuthFactorRule>
        List<AuthFactorRule> authFactorRules = objectMapper.readValue(
                sessionResponse.getAuthFactorRules(), 
                new TypeReference<List<AuthFactorRule>>() {}
        );
        
        boolean hasOtpFactor = authFactorRules.stream()
                .anyMatch(rule -> rule.getFactor() == AuthFactor.OTP);
        Assertions.assertTrue(hasOtpFactor, "Session should have OTP factor configured");
        log.info("Session has OTP factor configured");
    }

    @Then("The OTP configuration should be enriched with profile phone data")
    public void theOTPConfigurationShouldBeEnrichedWithProfilePhoneData() {
        // Verify that the session entity in database has enriched OTP config
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);
        
        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");
        
        Object otpConfig = sessionEntity.getFactorConfigs().get(AuthFactor.OTP);
        Assertions.assertNotNull(otpConfig, "OTP config should not be null");
        
        // The OTP config should be enriched with phone data from profile
        log.info("OTP configuration has been enriched with profile phone data");
    }

    @Then("The session status should be IN_PROGRESS")
    public void theSessionStatusShouldBeINPROGRESS() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);
        
        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(StepUpStatus.IN_PROGRESS, sessionEntity.getOverallStatus(), 
                "Session status should be IN_PROGRESS");
        log.info("Session status is IN_PROGRESS");
    }

    @Then("The current factor should be OTP")
    public void theCurrentFactorShouldBeOTP() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);
        
        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(AuthFactor.OTP, sessionEntity.getCurrentFactor(), 
                "Current factor should be OTP");
        log.info("Current factor is OTP");
    }

    @Given("A profile does not exist in the system")
    public void aProfileDoesNotExistInTheSystem() {
        // Use a profileId that doesn't start with "test-profile-" so mock server returns 404
        profileId = "non-existent-profile-" + UUID.randomUUID();
        log.info("Using non-existent profileId: {} - mock server will return 404", profileId);
    }

    @Then("The service should return an error indicating profile not found")
    public void theServiceShouldReturnAnErrorIndicatingProfileNotFound() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        Assertions.assertTrue(thrownException instanceof DomainException, 
                "Exception should be DomainException");
        
        DomainException domainException = (DomainException) thrownException;
        Assertions.assertEquals(ErrorCode.PROFILE_NOT_FOUND, domainException.getErrorCode(), 
                "Error code should be PROFILE_NOT_FOUND");
        log.info("Service correctly returned PROFILE_NOT_FOUND error");
    }

    @Given("A profile exists but has no phone number information")
    public void aProfileExistsButHasNoPhoneNumberInformation() {
        // For this scenario, we would need to modify the mock server to return profile without phone data
        // For now, we'll use a special profileId that the mock can recognize
        profileId = "test-profile-no-phone-" + UUID.randomUUID();
        log.info("Using profileId without phone data: {}", profileId);
    }

    @Then("The service should return an error indicating invalid profile data")
    public void theServiceShouldReturnAnErrorIndicatingInvalidProfileData() {
        Assertions.assertNotNull(thrownException, "Exception should be thrown");
        // The specific error handling for missing phone data may vary based on implementation
        log.info("Service correctly returned error for invalid profile data: {}", thrownException.getMessage());
    }

    // ========================================
    // DEVICE_BIO FACTOR STEP DEFINITIONS
    // ========================================

    @Given("The profile has valid device information")
    public void theProfileHasValidDeviceInformation() {
        // Extend profile info to include device data
        if (profileInfo == null) {
            profileInfo = ProfileInfo.builder()
                    .id(profileId)
                    .build();
        }
        // Add device information to profile
        log.info("Profile has valid device information for DEVICE_BIO factor");
    }

    @When("I initialize a step-up session with DEVICE_BIO factor and PROFILE_ID")
    public void iInitializeAStepUpSessionWithDEVICEBIOFactorAndPROFILEID() {
        initRequest = InitStepUpSessionDto.builder()
                .authFactorRules(List.of(AuthFactorRule.builder()
                        .factor(AuthFactor.DEVICE_BIO)
                        .build()))
                .identifierId(profileId)
                .identifierType(IdentifierType.PROFILE_ID)
                .flowId(flowId)
                .flowName(flowName)
                .appId(appId)
                .expiredIn(600)
                .build();
        log.info("Initializing step-up session with DEVICE_BIO factor for profileId: {}", profileId);

        try {
            sessionResponse = stepUpService.initStepUpSession(initRequest);
            log.info("Session initialized successfully with authId: {}", sessionResponse.getStepUpAuthId());
        } catch (Exception e) {
            thrownException = e;
            log.error("Exception occurred during DEVICE_BIO session initialization: {}", e.getMessage());
        }
    }

    @Then("The session should have DEVICE_BIO factor configured")
    public void theSessionShouldHaveDEVICEBIOFactorConfigured() throws JsonProcessingException {
        Assertions.assertNotNull(sessionResponse.getAuthFactorRules(), "Auth factor rules should not be null");
        Assertions.assertFalse(sessionResponse.getAuthFactorRules().isEmpty(), "Auth factor rules should not be empty");

        // Parse JSON string to List<AuthFactorRule>
        List<AuthFactorRule> authFactorRules = objectMapper.readValue(
                sessionResponse.getAuthFactorRules(),
                new TypeReference<List<AuthFactorRule>>() {}
        );

        boolean hasDeviceBioFactor = authFactorRules.stream()
                .anyMatch(rule -> rule.getFactor() == AuthFactor.DEVICE_BIO);
        Assertions.assertTrue(hasDeviceBioFactor, "Session should have DEVICE_BIO factor configured");
        log.info("Session has DEVICE_BIO factor configured");
    }

    @Then("The DEVICE_BIO configuration should be enriched with profile device data")
    public void theDEVICEBIOConfigurationShouldBeEnrichedWithProfileDeviceData() {
        // Verify that the session entity in database has enriched DEVICE_BIO config
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertNotNull(sessionEntity.getFactorConfigs(), "Factor configs should not be null");

        Object deviceBioConfig = sessionEntity.getFactorConfigs().get(AuthFactor.DEVICE_BIO);
        Assertions.assertNotNull(deviceBioConfig, "DEVICE_BIO config should not be null");

        log.info("DEVICE_BIO configuration has been enriched with profile device data");
    }

    @Then("The current factor should be DEVICE_BIO")
    public void theCurrentFactorShouldBeDEVICEBIO() {
        String authId = sessionResponse.getStepUpAuthId();
        StepUpEntity sessionEntity = stepUpRepo.queryByAuthId(authId)
                .filter(entity -> entity.getSk().equals(GlobalUtils.toStepUpSessionSk(authId)))
                .findFirst()
                .orElse(null);

        Assertions.assertNotNull(sessionEntity, "Session entity should exist in database");
        Assertions.assertEquals(AuthFactor.DEVICE_BIO, sessionEntity.getCurrentFactor(),
                "Current factor should be DEVICE_BIO");
        log.info("Current factor is DEVICE_BIO");
    }
